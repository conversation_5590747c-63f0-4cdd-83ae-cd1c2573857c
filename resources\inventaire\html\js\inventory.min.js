// @ts-nocheck
var type="normal",disabled=!1;function inventorySetup(t,e,a,n){if($("#playerInventory").html(""),$.each(t,(function(t,e){count=setCount(e),$("#playerInventory").append('<div class="inventory-slot" id="item-'+t+'" style="background-image: url(\'img/items/'+e.name+'.png\')"><div class="item-slot-wrapper"><div class="item-slot-header-wrapper"><div class="inventory-slot-number">'+count+'</div></div><div class="inventory-slot-label-box"><div class="inventory-slot-label-text">'+e.label+"</div></div></div></div>"),$("#item-"+t).data("item",e),$("#item-"+t).data("inventory","main")})),$("#playerInventoryFastItems").html(""),"item"==a){var i;for($("#drop"),i=1;i<5;i++)$("#playerInventoryFastItems").append('<div class="hotbar-item-slot" id="itemFast-'+i+'"><div class="item-slot-wrapper"><div class="item-hotslot-header-wrapper"><div class="keybind">'+i+"</div></div></div></div>");$.each(e,(function(t,e){count=setCount(e),$("#itemFast-"+e.slot).css("background-image","url('img/items/"+e.name+".png')"),$("#itemFast-"+e.slot).html('<div class="item-slot-wrapper"><div class="item-hotslot-header-wrapper"><div class="keybind">'+e.slot+'</div><div class="inventory-slot-number">'+count+'</div></div><div class="inventory-slot-label-box"><div class="inventory-slot-label-text">'+e.label+"</div></div></div>"),$("#itemFast-"+e.slot).data("item",e),$("#itemFast-"+e.slot).data("inventory","fast")}))}"clothe"==a&&$("#drop"),makeDraggables(),"item"==a&&$("#drop")}function makeDraggables(){$("#itemFast-1").droppable({drop:function(t,e){itemData=e.draggable.data("item"),itemInventory=e.draggable.data("inventory"),"normal"!==type||"main"!==itemInventory&&"fast"!==itemInventory||(disableInventory(500),$.post("http://inventaire/PutIntoFast",JSON.stringify({item:itemData,slot:1})))}}),$("#itemFast-2").droppable({drop:function(t,e){itemData=e.draggable.data("item"),itemInventory=e.draggable.data("inventory"),"normal"!==type||"main"!==itemInventory&&"fast"!==itemInventory||(disableInventory(500),$.post("http://inventaire/PutIntoFast",JSON.stringify({item:itemData,slot:2})))}}),$("#itemFast-3").droppable({drop:function(t,e){itemData=e.draggable.data("item"),itemInventory=e.draggable.data("inventory"),"normal"!==type||"main"!==itemInventory&&"fast"!==itemInventory||(disableInventory(500),$.post("http://inventaire/PutIntoFast",JSON.stringify({item:itemData,slot:3})))}}),$("#itemFast-4").droppable({drop:function(t,e){itemData=e.draggable.data("item"),itemInventory=e.draggable.data("inventory"),"normal"!==type||"main"!==itemInventory&&"fast"!==itemInventory||(disableInventory(500),$.post("http://inventaire/PutIntoFast",JSON.stringify({item:itemData,slot:4})))}})}function secondInventorySetup(t){$("#otherInventory").html(""),$.each(t,(function(t,e){count=setCount(e),$("#otherInventory").append('<div class="inventory-slot" id="itemOther-'+t+'" style="background-image: url(\'img/items/'+e.name+'.png\')"><div class="item-slot-wrapper"><div class="item-slot-header-wrapper"><div class="inventory-slot-number">'+count+'</div></div><div class="inventory-slot-label-box"><div class="inventory-slot-label-text">'+e.label+"</div></div></div></div>"),$("#itemOther-"+t).data("item",e),$("#itemOther-"+t).data("inventory","second")}))}function shopInventorySetup(t){$("#otherInventory").html(""),$.each(t,(function(t,e){cost=setCost(e),$("#otherInventory").append('<div class="inventory-slot" id="itemOther-'+t+'" style="background-image: url(\'img/items/'+e.name+'.png\')"><div class="item-slot-wrapper"><div class="item-slot-header-wrapper"><div class="inventory-slot-number">'+cost+'</div></div><div class="inventory-slot-label-box"><div class="inventory-slot-label-text">'+e.label+"</div></div></div></div>"),$("#itemOther-"+t).data("item",e),$("#itemOther-"+t).data("inventory","second")}))}function disableInventory(t){disabled=!0,setInterval((function(){disabled=!1}),t)}function setCount(t){return count=t.count,t.limit>0&&(count=t.count),"item_weapon"===t.type&&(0==count?count="":count='<img src="img/bullet.png" class="ammoIcon"> '+t.count),"item_account"!==t.type&&"item_money"!==t.type||(count=formatMoney(t.count)),count}function formatMoney(t,e,a,n){e=isNaN(e=Math.abs(e))?2:e,n=null==n?",":n;var i=t<0?"-":"",o=String(parseInt(t=Math.abs(Number(t)||0).toFixed(e))),r=(r=o.length)>3?r%3:0;return i+(r?o.substr(0,r)+n:"")+o.substr(r).replace(/(\d{3})(?=\d)/g,"$1"+n)}window.addEventListener("message",(function(t){"display"==t.data.action?(type=t.data.type,disabled=!1,"normal"===type?$(".info-div").hide():"trunk"===type?$(".info-div").show():"property"===type||"vault"===type?$(".info-div").hide():"player"===type&&$(".info-div").show(),$(".ui").fadeIn(100)):"hide"==t.data.action?($("#dialog").dialog("close"),$(".ui").fadeOut(100),$(".item").remove(),$("#otherInventory").html('<div id="noSecondInventoryMessage"></div>'),$("#noSecondInventoryMessage").html(invLocale.secondInventoryNotAvailable)):"setItems"==t.data.action?(inventorySetup(t.data.itemList,t.data.fastItems,t.data.crMenu),$(".info-div2").html(t.data.text),$(".inventory-slot, .hotbar-item-slot").draggable({helper:"clone",appendTo:"body",zIndex:99999,revert:"invalid",start:function(t,e){if(disabled)return!1;$(this).css("background-image","none"),itemData=$(this).data("item"),$("#drop").addClass("disabled"),$("#give").addClass("disabled"),$("#rename").addClass("disabled"),$("#use").addClass("disabled")},stop:function(){itemData=$(this).data("item"),void 0!==itemData&&void 0!==itemData.name&&($(this).css("background-image","url('img/items/"+itemData.name+".png'"),$("#drop").removeClass("disabled"),$("#use").removeClass("disabled"),$("#rename").removeClass("disabled"),$("#give").removeClass("disabled"))}})):"setSecondInventoryItems"==t.data.action?secondInventorySetup(t.data.itemList):"setShopInventoryItems"==t.data.action?shopInventorySetup(t.data.itemList):"setInfoText"==t.data.action?$(".info-div").html(t.data.text):"setWeightText"==t.data.action?$(".weighttrunk-div").html(t.data.text):"nearPlayers"==t.data.action&&($("#nearPlayers").html(""),$.each(t.data.players,(function(t,e){$("#nearPlayers").append('<button class="nearbyPlayerButton" data-player="'+e.player+'">'+e.label+" ("+e.player+")</button>")})),$("#dialog").dialog("open"),$(".nearbyPlayerButton").click((function(){$("#dialog").dialog("close"),player=$(this).data("player"),$.post("http://inventaire/GiveItem",JSON.stringify({player:player,item:t.data.item,number:parseInt($("#count").val())}))})))})),$((function(){$(".raccours1").click((function(){$(".ui").fadeIn(),$.post("https://inventaire/OngletInventory",JSON.stringify({type:"item"}))})),$(".raccours2").click((function(){$(".ui").fadeIn(),$.post("https://inventaire/OngletInventory",JSON.stringify({type:"clothe"}))}))})),$(document).mousedown((function(t){3==t.which&&(itemData=$(t.target).data("item"),null!=itemData&&null!=itemData.usable&&(itemInventory=$(t.target).data("inventory"),itemData.usable&&($(t.target).fadeIn(50),setTimeout((function(){$.post("https://inventaire/UseItem",JSON.stringify({item:itemData}))}),100),$(t.target).fadeOut(50))))})),$(document).mouseup((function(t){2==t.which&&(itemData=$(t.target).data("item"),null!=itemData&&null!=itemData.usable&&(itemInventory=$(t.target).data("inventory"),$(t.target).fadeIn(50),setTimeout((function(){$.post("https://inventaire/DropItem",JSON.stringify({item:itemData,number:1}))}),100),$(t.target).fadeOut(50)))})),$(document).dblclick((function(t){1==t.which&&(itemData=$(t.target).data("item"),null!=itemData&&null!=itemData.usable&&(itemInventory=$(t.target).data("inventory"),$(t.target).fadeIn(50),setTimeout((function(){$.post("https://inventaire/RenameCloth",JSON.stringify({item:itemData}))}),100),$(t.target).fadeOut(50)))})),$(document).ready((function(){$("#count").focus((function(){$(this).val("")})).blur((function(){""==$(this).val()&&$(this).val("1")})),$("#use").droppable({hoverClass:"hoverControl",drop:function(t,e){itemData=e.draggable.data("item"),itemData.usable&&$.post("http://inventaire/UseItem",JSON.stringify({item:itemData}))}}),$(".cloth-items .item-box").off().click((function(){$.post("http://inventaire/ChangeComponent",JSON.stringify({component:$(this).attr("id")}))})),$(".cloth-items2 .item-box2").off().click((function(){$.post("http://inventaire/ChangeComponent",JSON.stringify({component:$(this).attr("id")}))})),$(".buttoniconidcard .raccours3").off().click((function(){$.post("http://inventaire/IdCardShow",JSON.stringify())})),$("#give").droppable({hoverClass:"hoverControl",drop:function(t,e){player=$(this).data("player"),itemData=e.draggable.data("item"),$.post("http://inventaire/GetNearPlayers",JSON.stringify({player:player,item:itemData,number:parseInt($("#count").val())}))}}),$("#drop").droppable({hoverClass:"hoverControl",drop:function(t,e){itemData=e.draggable.data("item"),$.post("http://inventaire/DropItem",JSON.stringify({item:itemData,number:parseInt($("#count").val())}))}}),$("#rename").droppable({hoverClass:"hoverControl",drop:function(t,e){itemData=e.draggable.data("item"),$.post("http://inventaire/RenameCloth",JSON.stringify({item:itemData}))}}),$("#playerInventory").droppable({drop:function(t,e){itemData=e.draggable.data("item"),itemInventory=e.draggable.data("inventory"),"trunk"===type&&"second"===itemInventory?(disableInventory(500),$.post("http://inventaire/TakeFromTrunk",JSON.stringify({item:itemData,number:parseInt($("#count").val())}))):"property"===type&&"second"===itemInventory?(disableInventory(500),$.post("http://inventaire/TakeFromProperty",JSON.stringify({item:itemData,number:parseInt($("#count").val())}))):"normal"===type&&"fast"===itemInventory?(disableInventory(500),$.post("http://inventaire/TakeFromFast",JSON.stringify({item:itemData}))):"vault"===type&&"second"===itemInventory?(disableInventory(500),$.post("http://inventaire/TakeFromVault",JSON.stringify({item:itemData,number:parseInt($("#count").val())}))):"player"===type&&"second"===itemInventory&&(disableInventory(500),$.post("http://inventaire/TakeFromPlayer",JSON.stringify({item:itemData,number:parseInt($("#count").val())})))}}),$("#otherInventory").droppable({drop:function(t,e){itemData=e.draggable.data("item"),itemInventory=e.draggable.data("inventory"),"trunk"===type&&"main"===itemInventory?(disableInventory(500),$.post("http://inventaire/PutIntoTrunk",JSON.stringify({item:itemData,number:parseInt($("#count").val())}))):"property"===type&&"main"===itemInventory?(disableInventory(500),$.post("http://inventaire/PutIntoProperty",JSON.stringify({item:itemData,number:parseInt($("#count").val())}))):"vault"===type&&"main"===itemInventory?(disableInventory(500),$.post("http://inventaire/PutIntoVault",JSON.stringify({item:itemData,number:parseInt($("#count").val())}))):"player"===type&&"main"===itemInventory&&(disableInventory(500),$.post("http://inventaire/PutIntoPlayer",JSON.stringify({item:itemData,number:parseInt($("#count").val())})))}}),$("#count").on("keypress keyup blur",(function(t){$(this).val($(this).val().replace(/[^\d].+/,"")),(t.which<48||t.which>57)&&t.preventDefault()}))})),$.widget("ui.dialog",$.ui.dialog,{options:{clickOutside:!1,clickOutsideTrigger:""},open:function(){var t=$(this.options.clickOutsideTrigger),e=this;this.options.clickOutside&&$(document).on("click.ui.dialogClickOutside"+e.eventNamespace,(function(a){var n=$(a.target);0===n.closest($(t)).length&&0===n.closest($(e.uiDialog)).length&&e.close()})),this._super()},close:function(){$(document).off("click.ui.dialogClickOutside"+this.eventNamespace),this._super()}});