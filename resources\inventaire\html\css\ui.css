/* Import fonts from ox_inventory style */
@import url(https://fonts.googleapis.com/css?family=Poppins:200,400,500,600,700);
@import url(https://fonts.googleapis.com/css2?family=Open+Sans&display=swap);

@font-face {
    font-family: robotocondensedbold;
    src: url(../font/Vision-Bold.otf);
}

@font-face {
    font-family: robotocondensedmedium;
    src: url(../font/Vision-Heavy.otf);
}

@font-face {
    font-family: robotocondensedlight;
    src: url(../font/Vision-Black.otf);
}

/* Variables exactes d'ox_inventory */
:root {
    --font-family: Roboto;
    --font-family-bold: Roboto-Bold;
    --font-family-medium: Roboto-Medium;
    --font-family-regular: Roboto-Regular;

    /* Couleurs exactes d'ox_inventory */
    --mainColor: #22232c;
    --textColor: #c1c2c5;
    --secondaryColor: rgba(12, 12, 12, 0.4);
    --secondaryColorHighlight: #33343F;
    --secondaryColorLight: rgba(0, 0, 0, 0.5);
    --secondaryColorDark: rgba(12, 12, 12, 0.8);
    --primary-color: #5c1c88;

    /* Dimensions de grille d'ox_inventory */
    --gridCols: 5;
    --gridRows: 5;
    --gridSize: 10.2vh;
    --gridGap: 2px;
}

/* Style body exactement comme ox_inventory */
body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    height: 100vh;
    background: none !important;
    overflow: hidden !important;
    user-select: none;
}

* {
    padding: 0;
    margin: 0;
    color: var(--textColor);
    box-sizing: border-box;
}

/* Animation keyframes from ox_inventory */
@keyframes openmenu {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shake {
    0% { transform: translate(1px, 1px) rotate(0deg); }
    10% { transform: translate(-1px, -2px) rotate(-5deg); }
    20% { transform: translate(-3px, 0px) rotate(5deg); }
    30% { transform: translate(3px, 2px) rotate(0deg); }
    40% { transform: translate(1px, -1px) rotate(5deg); }
    50% { transform: translate(-1px, 2px) rotate(-5deg); }
    60% { transform: translate(-3px, 1px) rotate(0deg); }
    70% { transform: translate(3px, 1px) rotate(-5deg); }
    80% { transform: translate(-1px, -1px) rotate(5deg); }
    90% { transform: translate(1px, 2px) rotate(0deg); }
    100% { transform: translate(1px, -2px) rotate(-5deg); }
}

.ui {
    position: relative;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background-color: transparent;
    opacity: 1;
    display: block;
    z-index: 0;
    animation-name: openmenu;
    animation-duration: 0.6s;
}


.buttoniconidcard{
    position: absolute;
    top:14vh;
    right:71.5vh;
}

.buttoniconidcard *{
    display: flex;
    opacity: 0.7;
}
.buttoniconidcard .raccours3:hover{
    opacity: 0.2;
}
.buttoniconidcard img{
    width: 40%;
    height: 40%;
    right: 1vh;
}
.buttoniconidcard .raccours3:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.buttoniconidcard > .raccours3 > span{
    position: relative;
    right:1.9vh;
    font-size: 2vh;
}


.raccours3 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.raccours3:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.raccours3:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.raccours3 *{
  transform: rotate(90deg);
}
.raccours3:before {
    bottom: 0;
    height: 50%;
}
.raccours3 i{
    position: absolute;
    font-size: 4vh;
}


.raccourci2 {
    position: relative;
    top: 17.5vh;
    left: 6vh;
    display: table;
}
.raccours10,
.raccours11 {
    font-weight: bold;
    font-size: 3.3vh;
}

.autourname{
    font-weight: bold;
    font-size: 3.3vh;
}
.raccours10:hover {
    opacity: 0.7;
}
.autourname:hover {
    opacity: 0.7;
}

.buttoniconbag{
    position: absolute;
    top:14vh;
    right:93.5vh;
}

.buttoniconbag *{
    display: flex;
    opacity: 0.7;
}
.buttoniconbag .raccours1:hover{
    opacity: 0.2;
}
.buttoniconbag img{
    width: 40%;
    height: 40%;
    right: 1vh;
}
.buttoniconbag .raccours1:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.buttoniconbag > .raccours1 > span{
    position: relative;
    right:1.9vh;
    font-size: 2vh;
}


.raccours1 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.raccours1:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.raccours1:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.raccours1 *{
  transform: rotate(90deg);
}
.raccours1:before {
    bottom: 0;
    height: 50%;
}
.raccours1 i{
    position: absolute;
    font-size: 5vh;
    /*filter: drop-shadow(1px 1px 0 black) drop-shadow(-1px -1px 0 black);*/
}


.buttoniconvtm{
    position: absolute;
    top:14vh;
    right:82.5vh;
}

.buttoniconvtm *{
    display: flex;
    opacity: 0.7;
}
.buttoniconvtm .raccours2:hover{
    opacity: 0.2;
}
.buttoniconvtm img{
    width: 40%;
    height: 40%;
    right: 1vh;
}
.buttoniconvtm .raccours2:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.buttoniconvtm > .raccours2 > span{
    position: relative;
    right:1.9vh;
    font-size: 2vh;
}


.raccours2 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.raccours2:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.raccours2:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.raccours2 *{
  transform: rotate(90deg);
}
.raccours2:before {
    bottom: 0;
    height: 50%;
}
.raccours2 i{
    position: absolute;
    font-size: 5vh;
    /*filter: drop-shadow(1px 1px 0 black) drop-shadow(-1px -1px 0 black);*/
}


/* Modern inventory wrapper inspired by ox_inventory */
.inventory {
    position: absolute;
    left: 50vw;
    top: 50vh;
    transform: translate(-50%, -50%);
    width: 93.5vw;
    height: 70vh;
    color: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 40px;
    animation-name: openmenu;
    animation-duration: 0.6s;
}

/* Grille d'inventaire exactement comme ox_inventory */
#playerInventory {
    display: grid;
    grid-template-columns: repeat(var(--gridCols), var(--gridSize));
    grid-auto-rows: calc(var(--gridSize) + 0.22vh);
    gap: var(--gridGap);
    height: calc(var(--gridRows) * (var(--gridSize) + 0.22vh) + var(--gridRows) * var(--gridGap));
    overflow-y: scroll;
    width: 530px;
}

/* Custom scrollbar for player inventory */
#playerInventory::-webkit-scrollbar {
    width: 5px;
}

#playerInventory::-webkit-scrollbar-track {
    background: var(--background-dark);
    border-radius: 10px;
}

#playerInventory::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

#playerInventoryFastItems {
    display: flex;
    align-items: center;
    gap: var(--gridGap);
    justify-content: center;
    width: 100%;
    position: absolute;
    bottom: 2vh;
}

#otherInventory {
    display: grid;
    grid-template-columns: repeat(var(--gridCols), calc(var(--gridSize) - 0.5vh));
    grid-auto-rows: calc(var(--gridSize) + 0.22vh);
    gap: calc(var(--gridGap) * 2.5);
    height: calc(var(--gridRows) * (var(--gridSize) + 0.22vh) + var(--gridRows) * var(--gridGap));
    overflow-y: scroll;
    margin-top: 10px;
}

#count {
    border: none;
    outline: 0;
    font-size: 1.8vh;
}

input::-webkit-inner-spin-button,
input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

input[type=number] {
    -moz-appearance: textfield
}

/* Modern slot styling inspired by ox_inventory */
.slot {
    background-color: var(--background-dark);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 45px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    position: relative;
    color: #fff;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.slot:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background-color: rgba(0, 0, 0, 0.6);
}

.slotfix {
    background-color: var(--background-dark);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 45px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    position: relative;
    color: #fff;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.slotFast {
    width: 10.2vh;
    height: 10.2vh;
    background-color: var(--background-dark);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 45px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    position: relative;
    color: #fff;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.slotFast:hover, .slotfix:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background-color: rgba(0, 0, 0, 0.6);
}

.item-box2 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.item-box2:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.item-box2:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.item-box2 *{
  transform: rotate(90deg);
}
.item-box2:before {
    bottom: 0;
    height: 50%;
}
.item-box2 i{
    position: absolute;
    font-size: 4vh;

}
.cloth-items2{
    position: absolute;
    top:29vh;
    right:35vw;
}

.cloth-items2 *{
    display: flex;
    opacity: 0.7;
}
.cloth-items2 .item-box2:hover{
    opacity: 0.2;
}
.cloth-items2 img{
    width: 70%;
    height: 70%;
    right: 1vh;
}
.cloth-items2 .item-box2:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.cloth-items2 > .item-box2 > span{
    position: relative;
    right:1.9vh;
    font-size: 1.8vh;
}

.item-box {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.item-box:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.item-box:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.item-box *{
  transform: rotate(90deg);
}
.item-box:before {
    bottom: 0;
    height: 50%;
}
.item-box i{
    position: absolute;
    font-size: 4vh;
    /*filter: drop-shadow(1px 1px 0 black) drop-shadow(-1px -1px 0 black);*/
}
.cloth-items{
    position: absolute;
    top:29vh;
    right:57.5vw;
}

.cloth-items *{
    display: flex;
    opacity: 0.7;
}
.cloth-items .item-box:hover{
    opacity: 0.2;
}

.cloth-items img{
    width: 70%;
    height: 70%;
    right: 1vh;
}

.cloth-items .item-box:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.cloth-items > .item-box > span{
    position: relative;
    right:1.9vh;
    font-size: 1.8vh;
}

.slotFast {
    float: left;
    width: 9.2vh;
    height: 9.2vh;
    bottom: 250%;
    left: 64vh;
    color: #fff;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    margin: 0.5vh;
    border-radius: 3%;
    position: relative
}

.slotFast:hover {
    opacity: 0.8;
}

/* Modern item styling inspired by ox_inventory */
.item,
.item-other {
    width: 100%;
    height: 100%;
    background-size: 45px;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: all 0.2s ease;
    image-rendering: -webkit-optimize-contrast;
}

.item:hover,
.item-other:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* Modern item label styling */
.item-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--background-dark);
    font-family: var(--font-family), sans-serif;
    font-size: 9px;
    color: #fff;
    text-align: center;
    border-radius: 0 0 4px 4px;
    padding: 2px 3px;
    text-transform: none;
    overflow: hidden;
    text-overflow: ellipsis;
    z-index: 500;
}

.ui-draggable-dragging .item-count,
.ui-draggable-dragging .item-name {
    display: none
}

/* Modern item count styling inspired by ox_inventory */
.item-count {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--primary-color);
    color: #fff;
    height: 17px;
    border-radius: 0px 0px 0px 3px;
    width: 16px;
    font-weight: 400;
    padding: 3px;
    font-size: 12px;
    font-family: var(--font-family), sans-serif;
    text-align: center;
    z-index: 500;
}

.keybind {
    position: absolute;
    text-align: right;
    width: 11.4vh;
    margin-top: -15px;
    margin-left: -100px;
    height: 20px;
    z-index: 500;
    font-size: 0vh
}

.ammoIcon {
    width: 8px;
    height: 8px
}

.info-div {
    text-align: left;
    padding: 5px;
    width: 174px;
    position: absolute;
    font-size: 1.2vh;
    left: 64%;
    top: 1%;
    transform: translate(-50%, -50%)
}

.info-div2 {
    text-align: left;
    padding: 5px;
    width: 155px;
    position: absolute;
    font-size: 1.2vh;
    left: 30%;
    top: 1%;
    transform: translate(-50%, -50%)
}

/* Modern controls styling inspired by ox_inventory */
.controls-div {
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: center;
    align-items: center;
    margin: 0 20px;
}

.control {
    transition: 0.2s;
    padding: 8px 0;
    font-family: var(--font-family), sans-serif;
    font-size: 18px;
    text-align: center;
    outline: 0;
    border: 0;
    color: #fff;
    background-color: var(--background-dark);
    width: 6vw;
    border-radius: 4px;
    border: 0.5px solid var(--border-color);
}

.control2 {
    font-size: 14px;
    color: #fff;
    background-color: var(--background-darker);
    border-radius: 6px;
    border: 0.5px solid var(--border-color);
    transition: 0.2s;
    padding: 12px 8px;
    text-transform: uppercase;
    font-family: var(--font-family), sans-serif;
    width: 7vw;
    cursor: pointer;
    position: absolute;
    top: 59.5vh;
    left: 9vh;
}

.control3 {
    font-size: 14px;
    color: #fff;
    background-color: var(--background-darker);
    border-radius: 6px;
    border: 0.5px solid var(--border-color);
    transition: 0.2s;
    padding: 12px 8px;
    text-transform: uppercase;
    font-family: var(--font-family), sans-serif;
    width: 7vw;
    cursor: pointer;
    position: absolute;
    top: 55.8vh;
    left: 12vh;
}

.control:hover, .control2:hover, .control3:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Additional modern styles for new HTML structure */
.app-wrapper {
    height: 100%;
    width: 100%;
    color: #fff;
}

.inventory-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    gap: 40px;
}

.inventory-wrapper2 {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    animation-name: openmenu;
    animation-duration: 0.6s;
    height: 70%;
    gap: 40px;
}

.inventory-grid-wrapper {
    display: flex;
    flex-direction: column;
    gap: calc(var(--gridGap) * 2);
}

.inventory-grid-wrapper-user {
    display: flex;
    flex-direction: column;
    gap: calc(var(--gridGap) * 2);
}

.inventory-grid-header-wrapper-user {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.inventory-grid-header-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.WeightText {
    font-size: 16px;
    font-family: var(--font-family), sans-serif;
    color: var(--textColor);
}

.LabelText {
    font-size: 16px;
    font-family: var(--font-family), sans-serif;
    color: var(--textColor);
}

.inventory-control {
    display: flex;
    align-self: center;
}

.inventory-control .inventory-control-wrapper {
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: center;
    align-items: center;
}

.inventory-control .inventory-control-input {
    transition: 0.2s;
    padding: 8px 0;
    font-family: var(--font-family), sans-serif;
    font-size: 18px;
    text-align: center;
    outline: 0;
    border: 0;
    color: #fff;
    background-color: var(--background-dark);
    width: 6vw;
    border-radius: 4px;
    border: 0.5px solid var(--border-color);
}

.inventory-control .inventory-control-button {
    font-size: 14px;
    color: #fff;
    background-color: var(--background-darker);
    border-radius: 6px;
    border: 0.5px solid var(--border-color);
    transition: 0.2s;
    padding: 12px 8px;
    text-transform: uppercase;
    font-family: var(--font-family), sans-serif;
    width: 7vw;
    cursor: pointer;
}

.inventory-control .inventory-control-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.ButtonRow {
    display: flex;
    flex-direction: row;
    gap: 15px;
}

.info-sections {
    position: absolute;
    bottom: 20px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
}

.info-div, .info-div2 {
    font-family: var(--font-family), sans-serif;
    font-size: 14px;
    color: #fff;
}

/* Styles des slots exactement comme ox_inventory */
.inventory-slot {
    background-color: var(--secondaryColor);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 7vh;
    border-radius: 2.5%;
    image-rendering: -webkit-optimize-contrast;
    position: relative;
    color: var(--textColor);
    border-color: rgba(0, 0, 0, 0.2);
    border-style: inset;
    border-width: 1px;
}

.inventory-slot-number {
    background-color: white;
    color: black;
    height: 12px;
    border-top-left-radius: 0.25vh;
    border-bottom-right-radius: 0.25vh;
    padding: 3px;
    font-size: 12px;
    font-family: var(--font-family);
    position: absolute;
    top: 0;
    right: 0;
    text-align: center;
}

.inventory-slot-label-box {
    background-color: var(--mainColor);
    color: var(--textColor);
    text-align: center;
    border-bottom-left-radius: 0.25vh;
    border-bottom-right-radius: 0.25vh;
    border-top-color: rgba(0, 0, 0, 0.2);
    border-top-style: inset;
    border-top-width: 1px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}

.inventory-slot-label-text {
    text-transform: uppercase;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 2px 3px;
    font-weight: 400;
    font-family: var(--font-family);
    font-size: 12px;
}

.item-slot-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.item-slot-header-wrapper,
.item-hotslot-header-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: start;
}

.item-hotslot-header-wrapper {
    justify-content: space-between !important;
}

.hotbar-item-slot {
    width: var(--gridSize);
    height: var(--gridSize);
    background-color: var(--secondaryColor);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 7vh;
    border-radius: 2.5%;
    image-rendering: -webkit-optimize-contrast;
    position: relative;
    color: var(--textColor);
    border-color: rgba(0, 0, 0, 0.2);
    border-style: inset;
    border-width: 1px;
}

.keybind {
    position: absolute;
    top: 2px;
    left: 2px;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 10px;
    font-family: var(--font-family), sans-serif;
}


.control.disabled {
    background-color: rgba(36, 36, 36, 0.479);
}

.hoverControl {
    background-color: rgba(36, 36, 36, 0.479);
}

/* .used-item-slot {
    position: absolute;
    right: 50vh;
    bottom: 50vh;
    height: 125px;
    width: 110px;
    background-color: rgba(114, 114, 114, 0.308);
    border-style: solid;
    border-color: rgba(255, 255, 255, .2);
    border-width: 1px 1px 1px 1px
} */

/* #used-item-image {
    height: 12.5vh;
    width: 11vh;
    background-image: url(img/used-item.png);
    background-repeat: no-repeat;
    background-size: 0%;
    background-position-x: 0
}


#added-item-image {
    height: 125px;
    width: 110px;
    background-image: url(img/added-item.png);
    background-repeat: no-repeat;
    background-size: 80%;
    background-position-x: 0
} */
/* 
.item-name-bg {
    width: 100%;
    width: 117px;
    height: 20px;
    position: absolute;
    bottom: 0
} */

#otherInventory::-webkit-scrollbar-track,
#playerInventory::-webkit-scrollbar-track {
    background-color: none;
    border: none
}

#controlstrunk {
    width: 35vh;
    height: 50vh;
    float: left;
    position: relative;
    left: 62vh;
    top: -1.5vw;
}


.weighttrunk-div {
    text-align: left;
    padding: 0.5vh;
    width: 15.5vh;   
    position: absolute;
    font-size: 1.3vh;
    left: 22vw;
    top: 7%;
    transform: translate(-50%, -50%)
}


#otherInventory::-webkit-scrollbar,
#playerInventory::-webkit-scrollbar {
    width: 1vh
}

.nearbyPlayerButton {
    width: 100vh;
    margin-top: 0.5vh;
    display: block;
    text-decoration: none;
    padding: 0.2vh;
    color: rgba(255, 255, 255, .85);
    background-color: rgba(49, 49, 49, 0.274);
    border-radius: 9vh;
    text-shadow: none;
    font-size: 1.4vh!important;
    outline: 0;
    text-transform: none;
    text-align: center;
    line-height: 3vh;
    border: none
}

.nearbyPlayerButton:hover {
    background-color: rgba(36, 36, 36, 0.2)
}

#noSecondInventoryMessage {
    width: 61.5vh;
    height: 58vh;
    line-height: 58vh;
    text-align: center
}
/* 
@media (max-width: 100vh) {
    .inventory {
        width: 14vh;
    }

    #playerInventory {
        width: 600px;
        height: 500px;
        bottom: 105px;
    }

    #controls {
        width: 200px;
    }

    .control {
        width: 190px;
        height: 40px;
        left: 500px;
    }

    .raccours {
        top: 14%;
        right: -2%;
    }

    .info-div {
        padding: 15px;
        width: 174px;
        left: 105%;
        top: 0.5%;
    }

    .slotFast {
        width: 120px;
        height: 120px;
        bottom: 450%;
    }

    #otherInventory {
        width: 600px;
        height: 500px;
    }

    #noSecondInventoryMessage {
        width: 540px;
    }
} */