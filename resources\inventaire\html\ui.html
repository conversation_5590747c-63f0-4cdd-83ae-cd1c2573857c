<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventaire</title>
    <link rel="stylesheet" type="text/css" href="css/ui.css">
</head>

<body>

    <script src="js/config.js"></script>
    <script src="locales/en.js"></script>
    <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
    <script src="js/inventory.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <link href="https://fonts.cdnfonts.com/css/sf-pro-display?styles=98770" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300&display=swap" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0" />

    <script>
        $(function() {
            $("#dialog").dialog({
                autoOpen: false,
                clickOutside: true
            });
        });
    </script>

    <div class="ui">
        <div class="app-wrapper">
            <div class="inventory-wrapper">
                <div class="inventory-wrapper2">
                    <!-- Player Inventory Section -->
                    <div class="inventory-grid-wrapper-user">
                        <div class="inventory-grid-header-wrapper-user">
                            <div class="LabelText">Inventaire</div>
                            <div class="WeightText" id="weight"></div>
                        </div>
                        <div id="playerInventory" class="inventory-grid-container-user">
                        </div>
                    </div>

                    <!-- Control Section -->
                    <div class="inventory-control">
                        <div class="inventory-control-wrapper">
                            <input type="number" class="inventory-control-input" id="count" value="1" min="1">
                            <div class="ButtonRow">
                                <button class="inventory-control-button" id="give">
                                    <i class="fas fa-exchange"></i> Donner
                                </button>
                                <button class="inventory-control-button" id="drop">
                                    <i class="fas fa-trash"></i> Jeter
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Other Inventory Section -->
                    <div class="inventory-grid-wrapper">
                        <div class="inventory-grid-header-wrapper">
                            <div class="LabelText" id="otherInventoryLabel">Autour</div>
                            <div class="WeightText" id="otherWeight"></div>
                        </div>
                        <div id="otherInventory" class="inventory-grid-container">
                            <div id="noSecondInventoryMessage" class="LabelText">
                                Aucun inventaire secondaire disponible
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hotbar -->
                <div id="playerInventoryFastItems" class="hotbar-container">
                </div>
            </div>

            <!-- Info sections -->
            <div class="info-sections">
                <span class="info-div2"></span>
                <span class="info-div"></span>
            </div>
        </div>
    </div>
</div>
    <div class="cloth-inv">
        <div class="cloth-items">
            <div id="hat" class="item-box">
                <i class="fa-solid fa-hat-cowboy-side"></i>
            </div>
            <div id="vest" class="item-box">
                <i class="fa-solid fa-shirt"></i>
            </div>
            <div id="pants" class="item-box">
                <img src="img/pant.png"></img>
            </div>
            <div id="bag" class="item-box">
                <img src="img/bag.png"></img>
            </div>
        </div>
    </div>
    <div class="cloth-inv">
        <div class="cloth-items2">
            <div id="mask" class="item-box2">
                <img src="img/mask.png"></img>
            </div>
            <div id="glasses" class="item-box2">
                <img src="img/glasses.png"></img>
            </div>
            <div id="shirt" class="item-box2">
                <i class="fa-solid fa-vest"></i>
            </div>
            <div id="shoes" class="item-box2">
                <img src="img/shoes.png"></img>
            </div>
        </div>
    </div>
    <div class="cloth-inv2">
        <div class="buttoniconvtm">
            <div class="raccours2">
                <i class="material-symbols-outlined">checkroom</i>
        </div>
    </div>
    <div class="cloth-inv">
        <div class="buttoniconbag">
            <div class="raccours1">
                <i class="material-symbols-outlined">backpack</i>
        </div>
    </div>
    <div class="cloth-inv3">
        <div class="buttoniconidcard">
            <div class="raccours3" id ="idcard">
                <i class="fa-solid fa-address-card"></i>
        </div>
    </div>
</div>
</body>

</html>