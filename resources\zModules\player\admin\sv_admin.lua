ESX.RegisterServerCallback('GetGroup', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    local group = xPlayer.getGroup()
    cb(group)
end)

function sendToDiscordWithSpecialURL(name,message,color,url)
    local DiscordWebHook = 'https://discord.com/api/webhooks/1068957062505189416/FpBYjdFeb4eqUoPer_VJF2IwsWkG_oGVfwOnM-GWSCQTAC7x4Za8ZUyrvkGhkLtYUixf'
    -- Modify here your discordWebHook username = name, content = message,embeds = embeds
  
  local embeds = {
      {
          ["title"]=message,
          ["type"]="rich",
          ["color"]=color,
          ["footer"]={
          ["text"]=os.date("%Y/%m/%d %H:%M:%S"),
         },
      }
  }
  
    if message == nil or message == '' then return FALSE end
    PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

function getInfosPlayer(source)
	local identifier = GetPlayerIdentifiers(source)[1]
	local result = MySQL.Sync.fetchAll("SELECT * FROM users WHERE identifier = @identifier", {['@identifier'] = identifier})
	if result[1] ~= nil then
		local infos = result[1]

		return {
			identifier = infos['identifier'],
		}
	else
		return nil
	end
end

local FormattedToken = "Bot " .. "OTIyMjU5OTA4NTc2MDIyNTc4.Yb-3eA.9Oi3x8Eii7467VbjDD2NfPAOUdc"

function DiscordRequest(method, endpoint, jsondata)
    local data = nil
    PerformHttpRequest("https://discordapp.com/api/"..endpoint, function(errorCode, resultData, resultHeaders)
		data = {data=resultData, code=errorCode, headers=resultHeaders}
    end, method, #jsondata > 0 and json.encode(jsondata) or "", {["Content-Type"] = "application/json", ["Authorization"] = FormattedToken})

    while data == nil do
        Citizen.Wait(0)
    end
	
    return data
end

function GetDiscordName(user) 
    local discordId = nil
    local nameData = nil;
    for _, id in ipairs(GetPlayerIdentifiers(user)) do
        if string.match(id, "discord:") then
            discordId = string.gsub(id, "discord:", "")
            break
        end
    end
    if discordId then 
        local endpoint = ("users/%s"):format(discordId)
        local member = DiscordRequest("GET", endpoint, {})
        if member.code == 200 then
            local data = json.decode(member.data)
            if data ~= nil then 
                nameData = data.username .. "#" .. data.discriminator;
            end
        else 
        	print("[Badger_Perms] ERROR: Code 200 was not reached. DETAILS: " .. error_codes_defined[member.code])
        end
    end
    return nameData
end

function GetPermDiscord(src, idrole)
    local role = "non"

    for k, v in pairs(GetDiscordRoles(src)) do
        if v == idrole then
            role = true
        end
    end

    return role
end

function GetDiscordRoles(src)
	local discordId = nil
	for _, id in ipairs(GetPlayerIdentifiers(src)) do
		if string.match(id, "discord:") then
			discordId = string.gsub(id, "discord:", "")
			break
		end
	end

	if discordId then
		local endpoint = ("guilds/%s/members/%s"):format('930595342683078717', discordId)
		local member = DiscordRequest("GET", endpoint, {})
		if member.code == 200 then
			local data = json.decode(member.data)
			local roles = data.roles
			return roles
		end
    end
end

RegisterCommand("dis", function(source, args, rawCommand)
    print(json.encode(GetDiscordRoles(source)))
end)

RegisterCommand("dis2", function(source, args, rawCommand)
    local _src = source

    if GetPermDiscord(_src, '965239999337496577') then -- superadmin
        print('superadmin')
    elseif GetPermDiscord(_src, '965252570283118682') then -- dev
        print('dev')
    elseif GetPermDiscord(_src, '965241550814732329') then -- modo
        print('modo')
    end
end)

local AllPlayers = {}

RegisterNetEvent('UpdateAdminList')
AddEventHandler('UpdateAdminList', function()
    local v = source
    local xTarget = ESX.GetPlayerFromId(v)

    AllPlayers[v] = nil

    if getInfosPlayer(v) ~= nil then
        AllPlayers[v] = {
            name = xTarget.getName() or GetPlayerName(v),
            id = xTarget.source,
            dateofbirth = xTarget.get('dateofbirth') or "27/02/2000",
            group = xTarget.getGroup()

        }
    end
end)

RegisterNetEvent('AddPlayerInAta')
AddEventHandler('AddPlayerInAta', function(target, index)
    local v = target
	local xTarget = ESX.GetPlayerFromId(target)
    local xPlayer = ESX.GetPlayerFromId(source)
	local date = {
		year = os.date("*t").year, month = os.date("*t").month, day = os.date("*t").day, hour = os.date("*t").hour, min = os.date("*t").min, sec = os.date("*t").sec
	}

	MySQL.Sync.execute('UPDATE users SET timeata = @timeata, ata = @ata, diffata = @diffata WHERE identifier = @identifier', {
		['@identifier'] = xTarget.identifier,
		['@ata'] = 1,
		['@diffata'] = index,
		['@timeata'] = json.encode(date)
	})

    xPlayer.showNotification("~g~"..xTarget.getName().."~s~ a été mit en ATA pour une durée de : ~o~"..index.."min")

    Wait(250)

    if getInfosPlayer(v) ~= nil then
        AllPlayers[v] = {
            name = xTarget.getName() or GetPlayerName(v),
            id = xTarget.source,
            dateofbirth = xTarget.get('dateofbirth') or "27/02/2000",
            group = xTarget.getGroup()
        }
    end
end)

RegisterNetEvent('addListAdminMenu')
AddEventHandler('addListAdminMenu', function()
    local xPlayers 	= ESX.GetPlayers()

    for i = 1, #xPlayers, 1 do
        local xTarget = ESX.GetPlayerFromId(xPlayers[i])
        local v = xTarget.source
    
        if not AllPlayers[v] then
            if getInfosPlayer(v) ~= nil then
                AllPlayers[v] = {
                    name = xTarget.getName() or GetPlayerName(v),
                    id = xTarget.source,
                    dateofbirth = xTarget.get('dateofbirth') or "27/02/2000",
                    group = xTarget.getGroup()
                }
            end
        end
    end
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(source, xPlayer)
    local v = source
    local ids = ESX.ExtractIdentifiers(v)
    local xPlayer = ESX.GetPlayerFromId(v)

    if not AllPlayers[v] then
        if getInfosPlayer(v) ~= nil then
            AllPlayers[v] = {
                name = xPlayer.getName() or GetPlayerName(v),
                id = xPlayer.source,
                dateofbirth = xPlayer.get('dateofbirth') or "27/02/2000",
                group = xPlayer.getGroup()
            }
        end
    end
end)

AddEventHandler("playerDropped", function(reason)
    local source = source
    AllPlayers[source] = nil
end)
ESX.RegisterServerCallback('GetAllPlayers', function(source, cb)
    local xPlayers = ESX.GetPlayers()
    local players = {}

    for i=1, #xPlayers, 1 do
        local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
        table.insert(players, {
            name = xPlayer.getName() or GetPlayerName(v),
            id = xPlayer.source,
            dateofbirth = xPlayer.get('dateofbirth') or "27/02/2000",
            group = xPlayer.getGroup(),
        })
    end

    cb(players)
end)

RegisterNetEvent("MessageAdmin")
AddEventHandler("MessageAdmin", function(PlayerSelected, msg)
    local xPlayer = ESX.GetPlayerFromId(source)
    local targetXPlayer = ESX.GetPlayerFromId(PlayerSelected)
    
    if targetXPlayer ~= nil and xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        TriggerClientEvent('esx:showNotification', targetXPlayer.source, msg)
    else
        DropPlayer(source, "cheat MessageAdmin")
    end
end)

RegisterNetEvent("AnnonceAdmin")
AddEventHandler("AnnonceAdmin", function(msg)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        TriggerClientEvent('esx:showNotification', -1, "~b~Annonce Serveur~s~\n"..msg)
    else
        DropPlayer(source, "cheat AnnonceAdmin")
    end
end)

RegisterNetEvent('GotoPlayers')
AddEventHandler('GotoPlayers', function(target)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(target)

    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        if xTarget then
            local TargetCoords = xTarget.getCoords()
            TriggerClientEvent('GotoPlayers', source, TargetCoords)
        end
    else
        DropPlayer(source, "cheat GotoPlayers")
    end
end)

RegisterNetEvent("BringPlayers")
AddEventHandler("BringPlayers", function(target)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(target)

    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        if xTarget then
            local xPlayerCoords = xPlayer.getCoords()
            xTarget.setCoords(xPlayerCoords)
        end
    end
end)

RegisterNetEvent("freezePly")
AddEventHandler("freezePly", function(src, state)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        TriggerClientEvent("freezePlys", src, state)
    end
end)

RegisterNetEvent("ScreenshotAdmin")
AddEventHandler("ScreenshotAdmin", function(PlayerSelected)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        local targetXPlayer = ESX.GetPlayerFromId(PlayerSelected)

        TriggerClientEvent('ScreenshotAdmin', targetXPlayer.source, "https://discord.com/api/webhooks/1068957062505189416/FpBYjdFeb4eqUoPer_VJF2IwsWkG_oGVfwOnM-GWSCQTAC7x4Za8ZUyrvkGhkLtYUixf")
    end
end)

RegisterCommand("heal", function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        if tonumber(args[1]) then
            local xTarget = ESX.GetPlayerFromId(args[1])
            if xTarget then
                TriggerClientEvent('esx_basicneeds:healPlayer', xTarget.source)
                print(xTarget.source)
            end
        end
    end
end, false)

RegisterNetEvent("KickPlayer")
AddEventHandler("KickPlayer", function(PlayerSelected, raison)
	local targetXPlayer = ESX.GetPlayerFromId(PlayerSelected)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        if targetXPlayer ~= nil then
            DropPlayer(targetXPlayer.source, raison.." ("..GetPlayerName(source)..")")
        end
    end
end)

RegisterNetEvent("CreatePersoUser")
AddEventHandler("CreatePersoUser", function(target)
    local xPlayer = ESX.GetPlayerFromId(source)
	local xTarget = ESX.GetPlayerFromId(target)

    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        if xTarget then
            TriggerClientEvent('CreatePerso', xTarget.source)
        end
    else
        DropPlayer(xPlayer.source, "cheat CreatePerso")
    end
end)

ESX.RegisterServerCallback('GetSanction', function(source, cb, ply)
	local identifier = GetPlayerIdentifier(ply)

	MySQL.Async.fetchAll('SELECT * FROM sanction WHERE have = @have', {
		['@have'] = identifier
	}, function(result) 
		cb(result)  
	end)  
end)

RegisterNetEvent('SetSanction')
AddEventHandler('SetSanction', function(id, sanction)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        sendToDiscordWithSpecialURL("Sanction Ajouter", "**"..GetPlayerName(source).."** à ajouté la sanction **"..sanction.."** au joueur **"..GetPlayerName(id).."**", 2, "https://discord.com/api/webhooks/1068957062505189416/FpBYjdFeb4eqUoPer_VJF2IwsWkG_oGVfwOnM-GWSCQTAC7x4Za8ZUyrvkGhkLtYUixf")
        MySQL.Async.execute('INSERT INTO sanction (have, give, raison, date) VALUES (@have, @give, @raison, @date)', {
            ['@have']   = GetPlayerIdentifier(id), 
            ['@give']   = GetPlayerName(source),
            ['@raison']   = sanction,
            ['@date']   = os.date("*t").day.."/"..os.date("*t").month,
        })
    end
end)

RegisterNetEvent('DeleteSanction')
AddEventHandler('DeleteSanction', function(id, raison, plysanction)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        sendToDiscordWithSpecialURL("Sanction Delete", "**"..GetPlayerName(source).."** à supprimé la sanction **"..raison.."** du joueur **"..GetPlayerName(plysanction).."**", 14177041, "https://discord.com/api/webhooks/1068957062505189416/FpBYjdFeb4eqUoPer_VJF2IwsWkG_oGVfwOnM-GWSCQTAC7x4Za8ZUyrvkGhkLtYUixf")
        MySQL.Async.execute('DELETE FROM sanction WHERE id = @id', {
            ['@id'] = id 
        })
    end
end)

ESX.RegisterServerCallback('GetJobs', function(source, cb)
    MySQL.Async.fetchAll('SELECT name, label FROM jobs', {}, function(result)
        cb(result)
    end)
end)

ESX.RegisterServerCallback('GetJobsGrades', function(source, cb, job)
    MySQL.Async.fetchAll('SELECT id, name, label, grade FROM job_grades WHERE job_name = @job_name', {
        ['@job_name'] = job
    }, function(result)
        cb(result)
    end)
end)

ESX.RegisterServerCallback('GetListeItem', function(source, cb)
	MySQL.Async.fetchAll('SELECT label, name FROM items', {}, function(result)    
		cb(result)     
	end)    
end)

ESX.RegisterServerCallback("GetCrewGrade", function(source, cb, crew)
    MySQL.Async.fetchAll('SELECT * FROM crew_grades WHERE id_crew = @id_crew', {
        ['@id_crew'] = crew
    }, function(result)
        cb(result)
    end)
end)

RegisterNetEvent('EditOwnerCrew')
AddEventHandler('EditOwnerCrew', function(id_grade, id_crew, idjoueur, label, label_grade, rang_grade)
    local xPlayer = ESX.GetPlayerFromId(idjoueur)
    MySQL.Async.execute('UPDATE `crew_membres` SET `identifier` = @identifier, `id_grade` = @id_grade, `label_grade` = @label_grade, `rang_grade` = @rang_grade, `label` = @label WHERE id_crew = @id_crew', {
        ["@identifier"] = xPlayer.identifier,
        ["@id_grade"] = id_grade,
        ["@label_grade"] = label_grade,
        ["@rang_grade"] = rang_grade,
        ["@id_crew"] = id_crew,
        ["@label"] = label
    })
end)


RegisterNetEvent('DeleteCrew')
AddEventHandler('DeleteCrew', function(id, name)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        sendToDiscordWithSpecialURL("Crew delete", "**"..GetPlayerName(source).."** à supprimé le crew **"..name, 14177041, "https://discord.com/api/webhooks/1068957062505189416/FpBYjdFeb4eqUoPer_VJF2IwsWkG_oGVfwOnM-GWSCQTAC7x4Za8ZUyrvkGhkLtYUixf")

        MySQL.Async.execute('DELETE FROM crew_liste WHERE id_crew = @id_crew', {
            ['@id_crew'] = id 
        })
        MySQL.Async.execute('DELETE FROM crew_membres WHERE id_crew = @id_crew', {
            ['@id_crew'] = id 
        })
        MySQL.Async.execute('DELETE FROM crew_vehicles WHERE crew = @crew', {
            ['@crew'] = id 
        })
        MySQL.Async.execute('DELETE FROM crew_grades WHERE id_crew = @id_crew', {
            ['@id_crew'] = id 
        })
    end
end)

RegisterCommand("report", function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xPlayers 	= ESX.GetPlayers()

    if (#args > 0) then
        TriggerClientEvent("chat:addMessage", xPlayer.source, "Votre rapport a été envoyé")

        local reportreason = ""
        for x=1,#args do
            reportreason = reportreason .. " " .. args[x]
        end

        for i = 1, #xPlayers, 1 do
            local xAdmin = ESX.GetPlayerFromId(xPlayers[i])
            if xAdmin.getGroup() == 'superadmin' or xAdmin.getGroup() == 'admin' or xAdmin.getGroup() == 'moderator' then
                TriggerClientEvent("chat:addMessage", xAdmin.source,  "[^6REPORT^0] - (^6".. xPlayer.source .."^0) ^6" .. xPlayer.getName() .."^0 : " .. reportreason)
            end
        end
    else
        TriggerClientEvent("chat:addMessage", xPlayer.source, "Assurez-vous de suivre le format d'un report")
    end
end)

RegisterCommand("md", function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xPlayers 	= ESX.GetPlayers()

    if (#args > 0) then
        local reportreason = ""
        for x=1,#args do
            reportreason = reportreason .. " " .. args[x]
        end

        NamePly = GetPlayerName(source)

        for i = 1, #xPlayers, 1 do
            local xAdmin = ESX.GetPlayerFromId(xPlayers[i])
            if xAdmin.getGroup() == 'superadmin' or xAdmin.getGroup() == 'admin' or xAdmin.getGroup() == 'moderator' then
                TriggerClientEvent("chat:addMessage", xAdmin.source,  "(^1"..xPlayer.source.."^0 | ^1".. NamePly .."^0) : " .. reportreason)
            end
        end
    else
        TriggerClientEvent("chat:addMessage", xPlayer.source, "Assurez-vous de suivre le format")
    end
end)


local playerIdentity = {}
local alreadyRegistered = {}
local multichar = ESX.GetConfig().Multichar

local function deleteIdentityFromDatabase(xPlayer)
    MySQL.query.await(
        'UPDATE users SET firstname = ?, lastname = ?, dateofbirth = ?, sex = ?, height = ?, skin = ? WHERE identifier = ?',
        {nil, nil, nil, nil, nil, nil, xPlayer.identifier})

    if Config.FullCharDelete then
        MySQL.update.await('UPDATE addon_account_data SET money = 0 WHERE account_name IN (?) AND owner = ?',
            {{'bank_savings', 'caution'}, xPlayer.identifier})

        MySQL.prepare.await('UPDATE datastore_data SET data = ? WHERE name IN (?) AND owner = ?',
            {'\'{}\'', {'user_ears', 'user_glasses', 'user_helmet', 'user_mask'}, xPlayer.identifier})
    end
end

local function deleteIdentity(xPlayer)
    if not alreadyRegistered[xPlayer.identifier] then
        return
    end

    xPlayer.setName(('%s %s'):format(nil, nil))
    xPlayer.set('firstName', nil)
    xPlayer.set('lastName', nil)
    xPlayer.set('dateofbirth', nil)
    xPlayer.set('sex', nil)
    xPlayer.set('height', nil)
    deleteIdentityFromDatabase(xPlayer)
end

local function saveIdentityToDatabase(identifier, identity)
    MySQL.update.await(
        'UPDATE users SET firstname = ?, lastname = ?, dateofbirth = ?, sex = ?, height = ? WHERE identifier = ?',
        {identity.firstName, identity.lastName, identity.dateOfBirth, identity.sex, identity.height, identifier})
end

local function checkDOBFormat(str)
    str = tostring(str)
    if not string.match(str, '(%d%d)/(%d%d)/(%d%d%d%d)') then
        return false
    end
    
    local d, m, y = string.match(str, '(%d+)/(%d+)/(%d+)')
        
    m = tonumber(m)
    d = tonumber(d)
    y = tonumber(y)

    if ((d <= 0) or (d > 31)) or ((m <= 0) or (m > 12)) or ((y <= Config.LowestYear) or (y > Config.HighestYear)) then
        return false
    elseif m == 4 or m == 6 or m == 9 or m == 11 then
        return d < 30
    elseif m == 2 then
        if y % 400 == 0 or (y % 100 ~= 0 and y % 4 == 0) then
            return d < 29
        else
            return d < 28
        end
    else
        return d < 31
    end
end

local function formatDate(str)
    local d, m, y = string.match(str, '(%d+)/(%d+)/(%d+)')
    local date = str

    if Config.DateFormat == "MM/DD/YYYY" then
        date = m .. "/" .. d .. "/" .. y
    elseif Config.DateFormat == "YYYY/MM/DD" then
        date = y .. "/" .. m .. "/" .. d
    end

    return date
end

local function checkAlphanumeric(str)
    return (string.match(str, "%W"))
end

local function checkForNumbers(str)
    return (string.match(str, "%d"))
end

local function checkNameFormat(name)
    if not checkAlphanumeric(name) and not checkForNumbers(name) then
        local stringLength = string.len(name)
        return stringLength > 0 and stringLength < Config.MaxNameLength
    end

    return false
end

local function checkSexFormat(sex)
    if not sex then
        return false
    end
    return sex == "m" or sex == "M" or sex == "f" or sex == "F"
end

local function checkHeightFormat(height)
    local numHeight = tonumber(height) or 0
    return numHeight >= Config.MinHeight and numHeight <= Config.MaxHeight
end

local function convertToLowerCase(str)
    return string.lower(str)
end

local function convertFirstLetterToUpper(str)
    return str:gsub("^%l", string.upper)
end

local function formatName(name)
    local loweredName = convertToLowerCase(name)
    return convertFirstLetterToUpper(loweredName)
end

if Config.UseDeferrals then
    AddEventHandler('playerConnecting', function(playerName, setKickReason, deferrals)
        deferrals.defer()
        local playerId, identifier = source, ESX.GetIdentifier(source)
        Wait(100)

        if identifier then
            MySQL.single('SELECT firstname, lastname, dateofbirth, sex, height FROM users WHERE identifier = ?',
                {identifier}, function(result)
                    if result then
                        if result.firstname then
                            playerIdentity[identifier] = {
                                firstName = result.firstname,
                                lastName = result.lastname,
                                dateOfBirth = result.dateofbirth,
                                sex = result.sex,
                                height = result.height
                            }

                            deferrals.done()
                        else
                            deferrals.presentCard(
                                [==[{"type": "AdaptiveCard","body":[{"type":"Container","items":[{"type":"ColumnSet",
                                "columns":[{"type":"Column","items":[{"type":"Input.Text","placeholder":"First Name",
                                "id":"firstname","maxLength":15},{"type":"Input.Text","placeholder":"Date of Birth (MM/DD/YYYY)",
                                "id":"dateofbirth","maxLength":10}],"width":"stretch"},{"type":"Column","width":"stretch",
                                "items":[{"type":"Input.Text","placeholder":"Last Name","id":"lastname","maxLength":15},
                                {"type":"Input.Text","placeholder":"Height (48-96 inches)","id":"height","maxLength":2}]}]},
                                {"type":"Input.ChoiceSet","placeholder":"Sex","choices":[{"title":"Male","value":"m"},
                                {"title":"Female","value":"f"}],"style":"expanded","id":"sex"}]},{"type": "ActionSet",
                                "actions": [{"type":"Action.Submit","title":"Submit"}]}],
                                "$schema": "http://adaptivecards.io/schemas/adaptive-card.json","version":"1.0"}]==],
                                function(data)
                                    if data.firstname == '' or data.lastname == '' or data.dateofbirth == '' or data.sex ==
                                        '' or data.height == '' then
                                        deferrals.done(TranslateCap('data_incorrect'))
                                    else
                                        if checkNameFormat(data.firstname) and checkNameFormat(data.lastname) and
                                            checkDOBFormat(data.dateofbirth) and checkSexFormat(data.sex) and
                                            checkHeightFormat(data.height) then
                                            playerIdentity[identifier] = {
                                                firstName = formatName(data.firstname),
                                                lastName = formatName(data.lastname),
                                                dateOfBirth = data.dateofbirth,
                                                sex = data.sex,
                                                height = tonumber(data.height),
                                                saveToDatabase = true
                                            }

                                            deferrals.done()
                                        else
                                            deferrals.done(TranslateCap('invalid_format'))
                                        end
                                    end
                                end)
                        end
                    else
                        deferrals.presentCard(
                            [==[{"type": "AdaptiveCard","body":[{"type":"Container","items":[{"type":"ColumnSet","columns":[{
                            "type":"Column","items":[{"type":"Input.Text","placeholder":"First Name","id":"firstname",
                            "maxLength":15},{"type":"Input.Text","placeholder":"Date of Birth (MM/DD/YYYY)","id":"dateofbirth",
                            "maxLength":10}],"width":"stretch"},{"type":"Column","width":"stretch","items":[{"type":"Input.Text",
                            "placeholder":"Last Name","id":"lastname","maxLength":15},{"type":"Input.Text",
                            "placeholder":"Height (48-96 inches)","id":"height","maxLength":2}]}]},{"type":"Input.ChoiceSet",
                            "placeholder":"Sex","choices":[{"title":"Male","value":"m"},{"title":"Female","value":"f"}],
                            "style":"expanded","id":"sex"}]},{"type": "ActionSet","actions": [{"type":"Action.Submit",
                            "title":"Submit"}]}],"$schema": "http://adaptivecards.io/schemas/adaptive-card.json","version":"1.0"}]==],
                            function(data)
                                if data.firstname == '' or data.lastname == '' or data.dateofbirth == '' or data.sex == '' or data.height == '' then
                                    return deferrals.done(TranslateCap('data_incorrect'))
                                end
                                if not checkNameFormat(data.firstname) then
                                    return deferrals.done(TranslateCap('invalid_firstname_format'))
                                end
                                if not checkNameFormat(data.lastname) then
                                    return deferrals.done(TranslateCap('invalid_lastname_format'))
                                end
                                if not checkDOBFormat(data.dateofbirth) then
                                    return deferrals.done(TranslateCap('invalid_dob_format'))
                                end
                                if not checkSexFormat(data.sex) then
                                    return deferrals.done(TranslateCap('invalid_sex_format'))
                                end
                                if not checkHeightFormat(data.height) then
                                    return deferrals.done(TranslateCap('invalid_height_format'))
                                end
                                
                                playerIdentity[identifier] = {
                                    firstName = formatName(data.firstname),
                                    lastName = formatName(data.lastname),
                                    dateOfBirth = formatDate(data.dateofbirth),
                                    sex = data.sex,
                                    height = tonumber(data.height),
                                    saveToDatabase = true
                                }

                                deferrals.done()
                            end)
                    end
                end)
        else
            deferrals.done(TranslateCap('no_identifier'))
        end
    end)

    RegisterNetEvent('esx:playerLoaded')
    AddEventHandler('esx:playerLoaded', function(playerId, xPlayer)
        if not playerIdentity[xPlayer.identifier] then
            return xPlayer.kick(_('missing_identity'))
        end

        local currentIdentity = playerIdentity[xPlayer.identifier]
        xPlayer.setName(('%s %s'):format(currentIdentity.firstName, currentIdentity.lastName))
        xPlayer.set('firstName', currentIdentity.firstName)
        xPlayer.set('lastName', currentIdentity.lastName)
        xPlayer.set('dateofbirth', currentIdentity.dateOfBirth)
        xPlayer.set('sex', currentIdentity.sex)
        xPlayer.set('height', currentIdentity.height)
        if currentIdentity.saveToDatabase then
            saveIdentityToDatabase(xPlayer.identifier, currentIdentity)
        end

        Wait(0)
        alreadyRegistered[xPlayer.identifier] = true
        TriggerClientEvent('esx_identity:alreadyRegistered', xPlayer.source)
        playerIdentity[xPlayer.identifier] = nil
    end)
else
	local function setIdentity(xPlayer)
		if not alreadyRegistered[xPlayer.identifier] then
            return
        end
        local currentIdentity = playerIdentity[xPlayer.identifier]

        xPlayer.setName(('%s %s'):format(currentIdentity.firstName, currentIdentity.lastName))
        xPlayer.set('firstName', currentIdentity.firstName)
        xPlayer.set('lastName', currentIdentity.lastName)
        xPlayer.set('dateofbirth', currentIdentity.dateOfBirth)
        xPlayer.set('sex', currentIdentity.sex)
        xPlayer.set('height', currentIdentity.height)
        TriggerClientEvent('esx_identity:setPlayerData', xPlayer.source, currentIdentity)
        if currentIdentity.saveToDatabase then
            saveIdentityToDatabase(xPlayer.identifier, currentIdentity)
        end

        playerIdentity[xPlayer.identifier] = nil
	end
	
	local function checkIdentity(xPlayer)
		MySQL.single('SELECT firstname, lastname, dateofbirth, sex, height FROM users WHERE identifier = ?', {xPlayer.identifier},
            function(result)
                if not result then
                    return TriggerClientEvent('esx_identity:showRegisterIdentity', xPlayer.source)
                end
                if not result.firstname then
                    playerIdentity[xPlayer.identifier] = nil
                    alreadyRegistered[xPlayer.identifier] = false
                    return TriggerClientEvent('esx_identity:showRegisterIdentity', xPlayer.source)
                end

                playerIdentity[xPlayer.identifier] = {
                    firstName = result.firstname,
                    lastName = result.lastname,
                    dateOfBirth = result.dateofbirth,
                    sex = result.sex,
                    height = result.height
                }

                alreadyRegistered[xPlayer.identifier] = true
                setIdentity(xPlayer)
            end
        )
	end

	if not multichar then
		AddEventHandler('playerConnecting', function(playerName, setKickReason, deferrals)
			deferrals.defer()
			local playerId, identifier = source, ESX.GetIdentifier(source)
			Wait(40)

			if not identifier then
                return deferrals.done(TranslateCap('no_identifier'))
            end
            MySQL.single('SELECT firstname, lastname, dateofbirth, sex, height FROM users WHERE identifier = ?', {identifier}, 
                function(result)
                    if not result then
                        playerIdentity[identifier] = nil
                        alreadyRegistered[identifier] = false
                        return deferrals.done()
                    end
                    if not result.firstname then
                        playerIdentity[identifier] = nil
                        alreadyRegistered[identifier] = false
                        return deferrals.done()
                    end

                    playerIdentity[identifier] = {
                        firstName = result.firstname,
                        lastName = result.lastname,
                        dateOfBirth = result.dateofbirth,
                        sex = result.sex,
                        height = result.height
                    }

                    alreadyRegistered[identifier] = true

                    deferrals.done()
                end)
		end)

		AddEventHandler('onResourceStart', function(resource)
            if resource ~= GetCurrentResourceName() then
                return
            end
            Wait(300)

            while not ESX do Wait(0) end

            local xPlayers = ESX.GetExtendedPlayers()

            for i=1, #(xPlayers) do 
                if xPlayers[i] then
                    checkIdentity(xPlayers[i])
                end
            end
        end)

		RegisterNetEvent('esx:playerLoaded', function(playerId, xPlayer)
			local currentIdentity = playerIdentity[xPlayer.identifier]

            if currentIdentity and alreadyRegistered[xPlayer.identifier] then
                xPlayer.setName(('%s %s'):format(currentIdentity.firstName, currentIdentity.lastName))
                xPlayer.set('firstName', currentIdentity.firstName)
                xPlayer.set('lastName', currentIdentity.lastName)
                xPlayer.set('dateofbirth', currentIdentity.dateOfBirth)
                xPlayer.set('sex', currentIdentity.sex)
                xPlayer.set('height', currentIdentity.height)
                TriggerClientEvent('esx_identity:setPlayerData', xPlayer.source, currentIdentity)
                if currentIdentity.saveToDatabase then
                    saveIdentityToDatabase(xPlayer.identifier, currentIdentity)
                end

                Wait(0)

                TriggerClientEvent('esx_identity:alreadyRegistered', xPlayer.source)

                playerIdentity[xPlayer.identifier] = nil
            else
                TriggerClientEvent('esx_identity:showRegisterIdentity', xPlayer.source)
            end
		end)
	end

	ESX.RegisterServerCallback('esx_identity:registerIdentity', function(source, cb, data)
		local xPlayer = ESX.GetPlayerFromId(source)
		if not checkNameFormat(data.firstname) then
	            	TriggerClientEvent('esx:showNotification',source,TranslateCap('invalid_firstname_format'), "error")
            		return cb(false)
        	end
        	if not checkNameFormat(data.lastname) then
            		TriggerClientEvent('esx:showNotification',source,TranslateCap('invalid_lastname_format'), "error")
            		return cb(false)
        	end
        	if not checkSexFormat(data.sex) then
            		TriggerClientEvent('esx:showNotification',source,TranslateCap('invalid_sex_format'), "error")
            		return cb(false)
        	end
        	if not checkDOBFormat(data.dateofbirth) then
            		TriggerClientEvent('esx:showNotification',source,TranslateCap('invalid_dob_format'), "error")
            		return cb(false)
        	end
        	if not checkHeightFormat(data.height) then
            		TriggerClientEvent('esx:showNotification',source,TranslateCap('invalid_height_format'), "error")
            		return cb(false)
        	end
		if xPlayer then	
			if alreadyRegistered[xPlayer.identifier] then
				xPlayer.showNotification(TranslateCap('already_registered'), "error")
				return cb(false)
            end

            playerIdentity[xPlayer.identifier] = {
                firstName = formatName(data.firstname),
                lastName = formatName(data.lastname),
                dateOfBirth = formatDate(data.dateofbirth),
                sex = data.sex,
                height = data.height
            }

            local currentIdentity = playerIdentity[xPlayer.identifier]

            xPlayer.setName(('%s %s'):format(currentIdentity.firstName, currentIdentity.lastName))
            xPlayer.set('firstName', currentIdentity.firstName)
            xPlayer.set('lastName', currentIdentity.lastName)
            xPlayer.set('dateofbirth', currentIdentity.dateOfBirth)
            xPlayer.set('sex', currentIdentity.sex)
            xPlayer.set('height', currentIdentity.height)
            TriggerClientEvent('esx_identity:setPlayerData', xPlayer.source, currentIdentity)
            saveIdentityToDatabase(xPlayer.identifier, currentIdentity)
            alreadyRegistered[xPlayer.identifier] = true
            playerIdentity[xPlayer.identifier] = nil
            return cb(true)
        end

        if not multichar then
            TriggerClientEvent("esx:showNotification", source, TranslateCap('data_incorrect'), "error")
            return cb(false)
        end

        local formattedFirstName = formatName(data.firstname)
        local formattedLastName = formatName(data.lastname)
        local formattedDate = formatDate(data.dateofbirth)

        data.firstname = formattedFirstName
        data.lastname = formattedLastName
        data.dateofbirth = formattedDate
        local Identity = {
            firstName = formattedFirstName,
            lastName = formattedLastName,
            dateOfBirth = formattedDate,
            sex = data.sex,
            height = data.height
        }

        TriggerEvent('esx_identity:completedRegistration', source, data)
        TriggerClientEvent('esx_identity:setPlayerData', source, Identity)
        cb(true)
	end)
end

if Config.EnableCommands then
	ESX.RegisterCommand('char', 'user', function(xPlayer, args, showError)
        if xPlayer and xPlayer.getName() then
            xPlayer.showNotification(TranslateCap('active_character', xPlayer.getName()))
        else
            xPlayer.showNotification(TranslateCap('error_active_character'))
        end
    end, false, {help = TranslateCap('show_active_character')})

	ESX.RegisterCommand('chardel', 'user', function(xPlayer, args, showError)
        if xPlayer and xPlayer.getName() then
            if Config.UseDeferrals then
                xPlayer.kick(TranslateCap('deleted_identity'))
                Wait(1500)
                deleteIdentity(xPlayer)
                xPlayer.showNotification(TranslateCap('deleted_character'))
                playerIdentity[xPlayer.identifier] = nil
                alreadyRegistered[xPlayer.identifier] = false
            else
                deleteIdentity(xPlayer)
                xPlayer.showNotification(TranslateCap('deleted_character'))
                playerIdentity[xPlayer.identifier] = nil
                alreadyRegistered[xPlayer.identifier] = false
                TriggerClientEvent('esx_identity:showRegisterIdentity', xPlayer.source)
            end
        else
            xPlayer.showNotification(TranslateCap('error_delete_character'))
        end
    end, false, {help = TranslateCap('delete_character')})
end


ESX.RegisterCommand('xPlayerGetFirstName', 'user', function(xPlayer, args, showError)
    if xPlayer and xPlayer.get('firstName') then
        xPlayer.showNotification(TranslateCap('return_debug_xPlayer_get_first_name', xPlayer.get('firstName')))
    else
        xPlayer.showNotification(TranslateCap('error_debug_xPlayer_get_first_name'))
    end
end, false, {help = TranslateCap('debug_xPlayer_get_first_name')})

ESX.RegisterCommand('xPlayerGetLastName', 'user', function(xPlayer, args, showError)
    if xPlayer and xPlayer.get('lastName') then
        xPlayer.showNotification(TranslateCap('return_debug_xPlayer_get_last_name', xPlayer.get('lastName')))
    else
        xPlayer.showNotification(TranslateCap('error_debug_xPlayer_get_last_name'))
    end
end, false, {help = TranslateCap('debug_xPlayer_get_last_name')})

ESX.RegisterCommand('xPlayerGetFullName', 'user', function(xPlayer, args, showError)
    if xPlayer and xPlayer.getName() then
        xPlayer.showNotification(TranslateCap('return_debug_xPlayer_get_full_name', xPlayer.getName()))
    else
        xPlayer.showNotification(TranslateCap('error_debug_xPlayer_get_full_name'))
    end
end, false, {help = TranslateCap('debug_xPlayer_get_full_name')})

ESX.RegisterCommand('xPlayerGetSex', 'user', function(xPlayer, args, showError)
    if xPlayer and xPlayer.get('sex') then
        xPlayer.showNotification(TranslateCap('return_debug_xPlayer_get_sex', xPlayer.get('sex')))
    else
        xPlayer.showNotification(TranslateCap('error_debug_xPlayer_get_sex'))
    end
end, false, {help = TranslateCap('debug_xPlayer_get_sex')})

ESX.RegisterCommand('xPlayerGetDOB', 'user', function(xPlayer, args, showError)
    if xPlayer and xPlayer.get('dateofbirth') then
        xPlayer.showNotification(TranslateCap('return_debug_xPlayer_get_dob', xPlayer.get('dateofbirth')))
    else
        xPlayer.showNotification(TranslateCap('error_debug_xPlayer_get_dob'))
    end
end, false, {help = TranslateCap('debug_xPlayer_get_dob')})

ESX.RegisterCommand('xPlayerGetHeight', 'user', function(xPlayer, args, showError)
    if xPlayer and xPlayer.get('height') then
        xPlayer.showNotification(TranslateCap('return_debug_xPlayer_get_height', xPlayer.get('height')))
    else
        xPlayer.showNotification(TranslateCap('error_debug_xPlayer_get_height'))
    end
end, false, {help = TranslateCap('debug_xPlayer_get_height')})

local JoinCoolDown = {}
local BannedAlready = false
local BannedAlready2 = false
local isBypassing = false
local isBypassing2 = false
local DatabaseStuff = {}
local BannedAccounts = {}

AddEventHandler('esx:playerLoaded', function(source)
    local source = source
    local Steam = "NONE"
    local Lice = "NONE"
    local Live = "NONE"
    local Xbox = "NONE"
    local Discord = "NONE"
    local IP = "NONE"
    for k, v in ipairs(GetPlayerIdentifiers(source)) do
        if string.sub(v, 1,string.len("steam:")) == "steam:" then
            Steam = v
        elseif string.sub(v, 1,string.len("license:")) == "license:" then
            Lice = v
        elseif string.sub(v, 1,string.len("live:")) == "live:" then
            Live = v
        elseif string.sub(v, 1,string.len("xbl:")) == "xbl:" then
            Xbox = v
        elseif string.sub(v,1,string.len("discord:")) == "discord:" then
            Discord = v
        elseif string.sub(v, 1,string.len("ip:")) == "ip:" then
            IP = v
        end
    end
    if GetNumPlayerTokens(source) == 0 or GetNumPlayerTokens(source) == nil or GetNumPlayerTokens(source) < 0 or GetNumPlayerTokens(source) == "null" or GetNumPlayerTokens(source) == "**Invalid**" or not GetNumPlayerTokens(source) then
        DiscordLog(source, "Les numéros de jeton de joueur sont inconnus")
        DropPlayer(source, "Bansystem : \n Il y a un problème lors de la récupération de vos informations fivem \n Veuillez redémarrer FiveM.")
        return
    end
    for a, b in pairs(BannedAccounts) do
        for c, d in pairs(b) do 
            for e, f in pairs(json.decode(d.Tokens)) do
                for g = 0, GetNumPlayerTokens(source) - 1 do
                    if GetPlayerToken(source, g) == f or d.License == tostring(Lice) or d.Live == tostring(Live) or d.Xbox == tostring(Xbox) or d.Discord == tostring(Discord) or d.IP == tostring(IP) or d.Steam == tostring(Steam) then
                        if os.time() < tonumber(d.Expire) then
                            BannedAlready2 = true
                            if d.Steam ~= tostring(Steam) then
                                isBypassing2 = true
                            end
                            break
                        else
                            CreateUnbanThread(tostring(d.Steam))
                            break
                        end
                    end
                end
            end
        end
    end
    if BannedAlready2 then
        BannedAlready2 = false
        DiscordLog(source, "A essayé de rejoindre mais il / elle est banni (expulsé du serveur lorsqu'il est chargé sur le serveur (a été banni))")
	    DropPlayer(source, "raison du kick : vous avez été banni du serveur")
    end
    if isBypassing2 then
        isBypassing2 = false
        DiscordLog(source, "Tentative de connexion en utilisant la méthode de contournement (changement de Steam Hex (nouveau compte banni lorsqu'il est chargé sur le serveur))")
        BanNewAccount(tonumber(source), "Essayé de contourner", os.time() + (300 * 86400))
	    DropPlayer(source, "raison du kick : vous avez été banni du serveur")
    end
end)

AddEventHandler('Initiate:BanSql', function(hex, id, reason, name, day)
    local time
    if tonumber(day) == 0 then
	time = 9999
    else
	time = day
   end
    MySQL.Async.execute('UPDATE bansystem SET Reason = @Reason, isBanned = @isBanned, Expire = @Expire WHERE Steam = @Steam', 
    {
        ['@isBanned'] = 1,
        ['@Reason'] = reason,
        ['@Steam'] = hex,
        ['@Expire'] = os.time() + (time * 86400)
    })
    TriggerClientEvent('chat:addMessage', -1, {
        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 131, 0, 0.4); border-radius: 3px;"><i class="fas fa-exclamation-triangle"></i> [Punishment]<br>  {1}</div>',
        args = { name, '^1' .. name .. ' ^0Banni, Raison: ^1' ..reason.." ^0Durée: ^1"..time.." ^0 Jour(s)."}
    })
    DropPlayer(id, reason)
    SetTimeout(5000, function()
        ReloadBans()
    end)
end)

AddEventHandler('TargetPlayerIsOffline', function(hex, reason, xAdmin, day)
    local Ttime
    if tonumber(day) == 0 then
	Ttime = 9999
    else
	Ttime = day
    end
    MySQL.Async.fetchAll('SELECT Steam FROM bansystem WHERE Steam = @Steam',
    {
        ['@Steam'] = hex

    }, function(data)
        if data[1] then
            MySQL.Async.execute('UPDATE bansystem SET Reason = @Reason, isBanned = @isBanned, Expire = @Expire WHERE Steam = @Steam', 
            {
                ['@isBanned'] = 1,
                ['@Reason'] = reason,
                ['@Steam'] = hex,
                ['@Expire'] = os.time() + (Ttime * 86400)
            })
            TriggerClientEvent('chat:addMessage', -1, {
                template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 131, 0, 0.4); border-radius: 3px;"><i class="fas fa-exclamation-triangle"></i> [Punishment]<br>  {1}</div>',
                args = { hex, '^1' .. hex .. ' ^0Banni, Raison: ^1' ..reason.." ^0Durée: t ^1"..Ttime.." ^0 Jour(s)."}
            })
            SetTimeout(5000, function()
                ReloadBans()
            end)
        else
            TriggerClientEvent('chatMessage', xAdmin, "[Database]", {255, 0, 0}, " ^ 0Je ne peux pas trouver cet hexagone Steam. :( C'est faux")
        end
    end)
end)

AddEventHandler('playerConnecting', function(name, setKickReason)
    local source = source
    local Steam = "NONE"
    local Lice = "NONE"
    local Live = "NONE"
    local Xbox = "NONE"
    local Discord = "NONE"
    local IP = "NONE"
    for k, v in ipairs(GetPlayerIdentifiers(source)) do
        if string.sub(v, 1,string.len("steam:")) == "steam:" then
            Steam = v
        elseif string.sub(v, 1,string.len("license:")) == "license:" then
            Lice = v
        elseif string.sub(v, 1,string.len("live:")) == "live:" then
            Live = v
        elseif string.sub(v, 1,string.len("xbl:")) == "xbl:" then
            Xbox = v
        elseif string.sub(v,1,string.len("discord:")) == "discord:" then
            Discord = v
        elseif string.sub(v, 1,string.len("ip:")) == "ip:" then
            IP = v
        end
    end
    if Steam == nil or Lice == nil or Steam == "" or Lice == "" or Steam == "NONE" or Lice == "NONE" then
        setKickReason("\n \n Votre application Steam n'est pas ouverte, ouvrez d'abord l'application Steam. \n Redémarrez FiveM.")
        CancelEvent()
        return
    end
    if GetNumPlayerTokens(source) == 0 or GetNumPlayerTokens(source) == nil or GetNumPlayerTokens(source) < 0 or GetNumPlayerTokens(source) == "null" or GetNumPlayerTokens(source) == "**Invalid**" or not GetNumPlayerTokens(source) then
        DiscordLog(source, "Max Token Numbers Are nil")
        setKickReason("\n \n Un problème est survenu lors de la récupération de vos informations fivem \n Veuillez redémarrer FiveM.")
        CancelEvent()
        return
    end
    if JoinCoolDown[Steam] == nil then
        JoinCoolDown[Steam] = os.time()
    elseif os.time() - JoinCoolDown[Steam] < 15 then 
        setKickReason("\n \nCode d'erreur : #12\n \n Ne spammez pas le bouton de connexion")
        CancelEvent()
        return
    else
        JoinCoolDown[Steam] = nil
    end
    for a, b in pairs(BannedAccounts) do
        for c, d in pairs(b) do 
            for e, f in pairs(json.decode(d.Tokens)) do
                for g = 0, GetNumPlayerTokens(source) - 1 do
                    if GetPlayerToken(source, g) == f or d.License == tostring(Lice) or d.Live == tostring(Live) or d.Xbox == tostring(Xbox) or d.Discord == tostring(Discord) or d.IP == tostring(IP) or d.Steam == tostring(Steam) then
                        if os.time() < tonumber(d.Expire) then
                            BannedAlready = true
                            if d.Steam ~= tostring(Steam) then
                                isBypassing = true
                            end
                            setKickReason("\n \n Ban ID: #"..d.ID.."\n Raison: "..d.Reason.."\n Expiration: Vous avez été banni pour "..math.floor(((tonumber(d.Expire) - os.time())/86400)).." Day(s)! \nHWID: "..f)
                            CancelEvent()
                            break
                        else
                            CreateUnbanThread(tostring(d.Steam))
                            break
                        end
                    end
                end
            end
        end
    end
    if not BannedAlready and not isBypassing then
        InitiateDatabase(tonumber(source))
    end
    if BannedAlready then
        BannedAlready = false
        DiscordLog(source, "A essayé de rejoindre mais il / elle est banni (refusé de se joindre avant de charger sur le serveur)")
    end
    if isBypassing then
        isBypassing = false
        DiscordLog(source, "Essayé de rejoindre en utilisant la méthode de contournement (changement Steam Hex (nouveau compte banni avant le chargement sur le serveur))")
        BanNewAccount(tonumber(source), "Essayé de contourner le système d'interdiction", os.time() + (300 * 86400))
    end
end)

function CreateUnbanThread(Steam)
    MySQL.Async.fetchAll('SELECT Steam FROM bansystem WHERE Steam = @Steam',
    {
        ['@Steam'] = Steam

    }, function(data)
        if data[1] then
            MySQL.Async.execute('UPDATE bansystem SET Reason = @Reason, isBanned = @isBanned, Expire = @Expire WHERE Steam = @Steam', 
            {
                ['@isBanned'] = 0,
                ['@Reason'] = "",
                ['@Steam'] = Steam,
                ['@Expire'] = 0
            })
            SetTimeout(5000, function()
                ReloadBans()
            end)
        end
    end)
end

function InitiateDatabase(source)
    local source = source
    local ST = "None"
    local LC = "None"
    local LV = "None"
    local XB = "None"
    local DS = "None"
    local IiP = "None"
    for k, v in ipairs(GetPlayerIdentifiers(source)) do
        if string.sub(v, 1,string.len("steam:")) == "steam:" then
            ST  = v
        elseif string.sub(v, 1,string.len("license:")) == "license:" then
            LC  = v
        elseif string.sub(v, 1,string.len("live:")) == "live:" then
            LV  = v
        elseif string.sub(v, 1,string.len("xbl:")) == "xbl:" then
            Xbox = v
        elseif string.sub(v,1,string.len("discord:")) == "discord:" then
            DS = v
        elseif string.sub(v, 1,string.len("ip:")) == "ip:" then
            IiP = v
        end
    end
    if ST == "None" then print(source.." Failed To Create User") return end
    DatabaseStuff[ST] = {}
    for i = 0, GetNumPlayerTokens(source) - 1 do 
        table.insert(DatabaseStuff[ST], GetPlayerToken(source, i))
    end
    MySQL.Async.fetchAll('SELECT * FROM bansystem WHERE Steam = @Steam',
    {
        ['@Steam'] = ST

    }, function(data) 
        if data[1] == nil then
            MySQL.Async.execute('INSERT INTO bansystem (Steam, License, Tokens, Discord, IP, Xbox, Live, Reason, Expire, isBanned) VALUES (@Steam, @License, @Tokens, @Discord, @IP, @Xbox, @Live, @Reason, @Expire, @isBanned)',
            {
                ['@Steam'] = ST,
                ['@License'] = LC,
                ['@Discord'] = DS,
                ['@Xbox'] = XB,
                ['@IP'] = IiP,
                ['@Live'] = LV,
                ['@Reason'] = "",
                ['@Tokens'] = json.encode(DatabaseStuff[ST]),
                ['@Expire'] = 0,
                ['@isBanned'] = 0
            })
            DatabaseStuff[ST] = nil
        end 
    end)
end

function BanNewAccount(source, Reason, Time)
    local source = source
    local ST = "None"
    local LC = "None"
    local LV = "None"
    local XB = "None"
    local DS = "None"
    local IiP = "None"
    for k, v in ipairs(GetPlayerIdentifiers(source)) do
        if string.sub(v, 1,string.len("steam:")) == "steam:" then
            ST  = v
        elseif string.sub(v, 1,string.len("license:")) == "license:" then
            LC  = v
        elseif string.sub(v, 1,string.len("live:")) == "live:" then
            LV  = v
        elseif string.sub(v, 1,string.len("xbl:")) == "xbl:" then
            Xbox = v
        elseif string.sub(v,1,string.len("discord:")) == "discord:" then
            DS = v
        elseif string.sub(v, 1,string.len("ip:")) == "ip:" then
            IiP = v
        end
    end
    if ST == "None" then print(source.." Failed To Create User") return end
    DatabaseStuff[ST] = {}
    for i = 0, GetNumPlayerTokens(source) - 1 do 
        table.insert(DatabaseStuff[ST], GetPlayerToken(source, i))
    end
    MySQL.Async.fetchAll('SELECT * FROM bansystem WHERE Steam = @Steam',
    {
        ['@Steam'] = ST

    }, function(data) 
        if data[1] == nil then
            MySQL.Async.execute('INSERT INTO bansystem (Steam, License, Tokens, Discord, IP, Xbox, Live, Reason, Expire, isBanned) VALUES (@Steam, @License, @Tokens, @Discord, @IP, @Xbox, @Live, @Reason, @Expire, @isBanned)',
            {
                ['@Steam'] = ST,
                ['@License'] = LC,
                ['@Discord'] = DS,
                ['@Xbox'] = XB,
                ['@IP'] = IiP,
                ['@Live'] = LV,
                ['@Reason'] = Reason,
                ['@Tokens'] = json.encode(DatabaseStuff[ST]),
                ['@Expire'] = Time,
                ['@isBanned'] = 1
            })
            DatabaseStuff[ST] = nil
            SetTimeout(5000, function()
                ReloadBans()
            end)
        end 
    end)
end

RegisterCommand('banreload', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        ReloadBans()
        TriggerClientEvent('chatMessage', source, "[BanSystem]", {255, 0, 0}, " ^0Ban List Reloaded.")
    end
end)

RegisterServerEvent("Bansystem:BanMe")
AddEventHandler("Bansystem:BanMe", function(Reason, Time)
    local source = source
    for k, v in ipairs(GetPlayerIdentifiers(source)) do
        if string.sub(v, 1, string.len("steam:")) == "steam:" then
            Cheat = v
        end
    end
    TriggerEvent('Initiate:BanSql', Cheat, tonumber(source), tostring(Reason), GetPlayerName(source), tonumber(Time))
end)

function BanThis(source, Reason, Times)
    local time = Times
    for k, v in ipairs(GetPlayerIdentifiers(source)) do
        if string.sub(v, 1, string.len("steam:")) == "steam:" then
            STP = v
        end
    end
    if Times == nil or not Times then
        time = 365
    end
    TriggerEvent('Initiate:BanSql', STP, tonumber(source), tostring(Reason), GetPlayerName(source), tonumber(time))
end

RegisterCommand('ban', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    local target = tonumber(args[1])
    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        if args[1] then
            if tonumber(args[2]) then
                if tostring(args[3]) then
                    if tonumber(args[1]) then
                        if GetPlayerName(target) then
                            for k, v in ipairs(GetPlayerIdentifiers(target)) do
                                if string.sub(v, 1, string.len("steam:")) == "steam:" then
                                    Hex = v
                                end
                            end
                            TriggerEvent('Initiate:BanSql', Hex, tonumber(target), table.concat(args, " ",3), GetPlayerName(target), tonumber(args[2]))
                        else
                            TriggerClientEvent('chatMessage', source, "[BanSystem]", {255, 0, 0}, " ^0Player Is Not Online.")
                        end
                    else
                        if string.find(args[1], "steam:") ~= nil then
                            TriggerEvent('TargetPlayerIsOffline', args[1], table.concat(args, " ",3), tonumber(xPlayer.source), tonumber(args[2]))
                        else
                            TriggerClientEvent('chatMessage', source, "[BanSystem]", {255, 0, 0}, " ^0Incorrect Steam Hex.")
                        end
                    end
                else
                    TriggerClientEvent('chatMessage', source, "[BanSystem]", {255, 0, 0}, " ^0Please Enter Ban Reason.")
                end
            else
                TriggerClientEvent('chatMessage', source, "[BanSystem]", {255, 0, 0}, " ^0Plaease Enter Ban Duration.")
            end
        else
            TriggerClientEvent('chatMessage', source, "[BanSystem]", {255, 0, 0}, " ^0Please Enter Server ID Or Steam Hex.")
        end
    else
        if source ~= 0 then
            TriggerClientEvent('chatMessage', source, "[BanSystem]", {255, 0, 0}, " ^0You Are Not An Admin.")
        end
    end
end)

RegisterServerEvent("Bansystem:CheckBan")
AddEventHandler("Bansystem:CheckBan", function(hex)
    local source = source
    MySQL.Async.fetchAll('SELECT * FROM bansystem WHERE Steam = @Steam',
    {
        ['@Steam'] = hex

    }, function(data)
        if data[1] then
            if data[1].isBanned == 1 then
                DiscordLog(source, "Tried To Bypass")
                DropPlayer(source, "kick reason: you were banned")
            end
        end
    end)
end)

RegisterCommand('unban', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer.getGroup() == 'superadmin' or xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'moderator' then
        if tostring(args[1]) then
            MySQL.Async.fetchAll('SELECT Steam FROM bansystem WHERE Steam = @Steam',
            {
                ['@Steam'] = args[1]
    
            }, function(data)
                if data[1] then
                    MySQL.Async.execute('UPDATE bansystem SET Reason = @Reason, isBanned = @isBanned, Expire = @Expire WHERE Steam = @Steam', 
                    {
                        ['@isBanned'] = 0,
                        ['@Reason'] = "",
                        ['@Steam'] = args[1],
                        ['@Expire'] = 0
                    })
                    SetTimeout(5000, function()
                        ReloadBans()
                    end)
                    TriggerClientEvent('chatMessage', source, "[BanSystem]", {255, 0, 0}, " ^2Unban Success.")
                else
                    TriggerClientEvent('chatMessage', source, "[BanSystem]", {255, 0, 0}, " ^0The Entered Steam Is Incorrect.")
                end
            end)
        else
            TriggerClientEvent('chatMessage', source, "[BanSystem]", {255, 0, 0}, " ^0The Entered Steam Is Incorrect.")
        end
    else
        if source ~= 0 then
            TriggerClientEvent('chatMessage', source, "[BanSystem]", {255, 0, 0}, " ^0You Are Not An Admin.")
        end
    end
end)

function ReloadBans()
    Citizen.CreateThread(function()
        BannedAccounts = {}
        MySQL.Async.fetchAll('SELECT * FROM bansystem', {}, function(info)
            for i = 1, #info do
                if info[i].isBanned == 1 then
                    Citizen.Wait(2)
                    table.insert(BannedAccounts, {info[i]})
                end
            end
        end)
    end)
end

MySQL.ready(function()
	ReloadBans()
    print("Ban List Loaded")
end)

function DiscordLog(source, method)
    PerformHttpRequest('https://discord.com/api/webhooks/1068957062505189416/FpBYjdFeb4eqUoPer_VJF2IwsWkG_oGVfwOnM-GWSCQTAC7x4Za8ZUyrvkGhkLtYUixf', function(err, text, headers)
    end, 'POST',
    json.encode({
    embeds =  {{["color"] = 202020,
                ["author"] = {["name"] = 'Logs BanSystem ',
                ["icon_url"] = ''},
                ["description"] = "**Joueur:** " ..GetPlayerName(source).. "\n" .. "**ID Ban:** " .. source.. "\n" .. "**Raison:** " .. method .."",
                ["footer"] = {["text"] = "-"..os.date("%x %X  %p"),
                ["icon_url"] = '',},}
                },
    }),
    {['Content-Type'] = 'application/json'
    })
end
